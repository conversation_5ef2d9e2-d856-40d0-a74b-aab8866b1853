<?php

namespace Tests\Feature\General\Reservation;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Reservation::class)]
class ReservationEditTimeFormatTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Field $field;

    private Utility $utility;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'status' => 'Active',
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
        ]);
        $this->utility = Utility::factory()->create([
            'hourly_rate' => 25.00,
        ]);
    }

    #[Test]
    public function edit_form_handles_time_format_with_seconds()
    {
        // Arrange - Create reservation with HH:MM:SS time format
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3), // 3 days in future to ensure it's modifiable
            'start_time' => '18:00:00', // HH:MM:SS format
            'end_time' => '20:30:00',   // HH:MM:SS format
            'duration_hours' => 2.5,
            'total_cost' => 125.00,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Check that the page contains the time normalization function
        $response->assertSee('normalizeTimeFormat');
        $response->assertSee('Time format normalized');

        // Check that existing reservation data is properly set in JavaScript
        $response->assertSee("start_time: normalizeTimeFormat('18:00:00')", false);
        $response->assertSee("end_time: normalizeTimeFormat('20:30:00')", false);

        // Verify the normalization function is present
        $response->assertSee('timeString.substring(0, 5)');
        $response->assertSee('Time format normalized', false);
    }

    #[Test]
    public function edit_form_handles_time_format_without_seconds()
    {
        // Arrange - Create reservation with HH:MM time format
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3),
            'start_time' => '18:00', // HH:MM format
            'end_time' => '20:30',   // HH:MM format
            'duration_hours' => 2.5,
            'total_cost' => 125.00,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Check that existing reservation data is properly set in JavaScript
        $response->assertSee("start_time: normalizeTimeFormat('18:00')", false);
        $response->assertSee("end_time: normalizeTimeFormat('20:30')", false);

        // Verify the normalization function handles both formats
        $response->assertSee('normalizeTimeFormat');
    }

    #[Test]
    public function edit_form_contains_time_comparison_logic_with_normalization()
    {
        // Arrange
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3),
            'start_time' => '14:00:00',
            'end_time' => '16:30:00',
            'duration_hours' => 2.5,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Check that time comparison logic uses normalization
        $response->assertSee('normalizeTimeFormat(slot.start_time) === targetValue');
        $response->assertSee('normalizeTimeFormat(option.value) === currentEndTime');
        $response->assertSee('normalizeTimeFormat(option.value) === normalizedEndTime');
        $response->assertSee('normalizeTimeFormat(option.value) === normalizedStartTime');
    }

    #[Test]
    public function edit_form_javascript_variables_are_properly_escaped()
    {
        // Arrange - Test with various time formats
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3),
            'start_time' => '09:30:00',
            'end_time' => '12:45:00',
            'duration_hours' => 3.25,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Verify that the JavaScript variables are properly formatted
        $response->assertSee("field_id: '{$reservation->field_id}'", false);
        $response->assertSee("booking_date: '{$reservation->booking_date->format('Y-m-d')}'", false);
        $response->assertSee("start_time: normalizeTimeFormat('09:30:00')", false);
        $response->assertSee("end_time: normalizeTimeFormat('12:45:00')", false);
        $response->assertSee("duration_hours: '{$reservation->duration_hours}'", false);
    }

    #[Test]
    public function edit_form_handles_edge_cases_in_time_normalization()
    {
        // Arrange
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3),
            'start_time' => '00:00:00', // Midnight with seconds
            'end_time' => '23:59:00',   // Late night with seconds
            'duration_hours' => 23.98,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Check that edge case times are handled correctly
        $response->assertSee("start_time: normalizeTimeFormat('00:00:00')", false);
        $response->assertSee("end_time: normalizeTimeFormat('23:59:00')", false);

        // Verify the normalization function handles edge cases
        $response->assertSee('if (!timeString) return timeString');
        $response->assertSee('timeString.length', false);
    }
}
